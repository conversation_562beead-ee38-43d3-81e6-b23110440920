import http from '../utils/http';

export default {
  // 获取权益卡张数
  queryCount: (params, config) => http.get('/api-member/api/v1/nologin/card/count', params, config),
  // 查询权益卡信息
  queryCardInfo: (params, config) => http.get('/api-member/api/v1/nologin/cardInfo', params, config),
  // 查询权益卡列表
  queryCardList: (params, config) => http.get('/api-member/api/v1/card/list', params, config),
  // 卡权益
  pbmCardRights: (params, config) => http.get('/api-member/api/v1/card/rights', params, config),
  // 被保人列表
  queryInsured: (params, config) => http.get('/api-member/v1/insuranceinfo/queryInsuredByPhone', params, config),
  // 激活卡
  activateCard: (params, config) => http.post('/api-member/api/v1/card/simple/activate', params, config),
  // 校验卡密
  check: (params, config) => http.post('/api-member/api/v1/nologin/card/activate/check', params, config),
  // 激活权益卡
  activate: (params, config) => http.post('/api/v1/card/active', params, config),
  // 权益卡续期激活
  renew: (params, config) => http.get('/api-member/api/v1/card/renew', params, config),
  // 卡种列表
  queryBanner: (params, config) => http.get('/api-config/v2/advert/banner/list', params, config),
  // 权益介绍
  rightsIntro: (params, config) => http.get('/api-member/api/v1/pbm/rights/intro/' + params, {}, config),
  // 添加保障人员
  addFamily: (params, config) => http.post('/api-member/api/v1/card/members/add', params, config),
  // 修改预约信息
  update: (params, config) => http.post('/api-member/api/v1/card/booking/update', params, config),
  // 结算校验
  signCheck: (params, config) => http.get('/api-member/v1/sign/info/' + params, {}, config),
  // 结算提交
  sign: (params, config) => http.post('/api/v1/sign', params, config),
  // 查询hpv价格
  getPrice: (params, config) => http.post('/api-member/api/v1/card/hpv/price', params, config),
  // 预约hpv疫苗预约
  bookingHPV: (params, config) => http.post('/api-member/api/v1/card/booking/hpv', params, config),
  // 预约支付
  hpvPay: (params, config) => http.get('/api-member/api/v1/card/booking/pay/' + params, {}, config),
  // 取消预约
  cancel: (params, config) => http.post('/api-member/api/v1/card/booking/cancel', params, config),

  // 单个权益详细信息
  queryRight: (params, config) => http.get('/common-member/api/pbm/rights/info/' + params, config),
  //pv点击统计
  /**
   * RIGHTS("权益预约")
   * RIGHTS_ACTIVE("权益激活")
   * RIGHTS_URL("权益跳转")
   * HEALTH("健康档案")
   * DRUG_PLAN("用药计划")
   */
  getPvUv: (params, config) => http.get('/api-member/api/v1/pv', params, config),
  // 预约
  reserve: (params, config) => http.post('/api-member/api/v1/card/booking', params, config),
  // 预约列表
  queryOrders: (params, config) => http.post('/api-member/api/v1/card/booking/list', params, config),
  // 预约列表详情
  queryOrderDetail: (params, config) => http.get('/api-member/api/v1/card/booking/info', params, config),
  // 修改预约信息
  updateReserve: (params, config) => http.post('/api-member/api/v1/card/booking/update', params, config),

  //查询权益卡协议/手册
  queryAgreementList: (params, config) => http.get('/common-member/api/v1/agreement/list' + params, config),
  //查询权益卡协议/手册
  queryAgreementListPost: (params, config) => http.post('/common-member/api/v1/agreement/list', params, config),
}
