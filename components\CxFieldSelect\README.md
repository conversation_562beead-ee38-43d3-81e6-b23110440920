## 下拉选组件

由Field和ActionSheet 两个组件组合而成。

### 组件属性
属性包括Field（除readonly、clickable之外）的所有属性，和本组件特定属性

[Field的官方属性](https://youzan.github.io/vant/#/zh-CN/field#props)


**本组件特定属性如下：**

|参数	|说明	|类型	|默认值|
|--|--|--|--|
|options|	选项列表数据，与[actionSheet组件的actions属性](https://youzan.github.io/vant/#/zh-CN/action-sheet#props)一致 | array | [] |
|keyMap|	设置数据项中分别与“标签”和“值”对应的key, 如 ['name', 'id'] | array | ['label', 'value'] |
| optionsTitle | 选项列表的标题，实际是actionSheet组件的description属性 | string | '' |
| actionSheetProps | 组件内actionSheet组件（除actions、description）的所有属性的对象集合 | object | {} |

<br/>

keyMap实例说明

如：现有一个项目列表，我们想选择对应项目，绑定值为项目的id。可以如下设置。

```javascript
let items = [
  {
    id: 001,
    name: '项目一',
    desc: '项目一描述',
    addr: '上海'
  },
  {
    id: 002,
    name: '项目二',
    desc: '项目二描述',
    addr: '北京'
  },
];
```

```jsx
<cx-field-select
  label = "所属项目：" 
  placeholder = "请选择所属项目"
  v-model = "itemId" 
  :options = "items"
  :keyMap = "['name', 'id']"
  optionsTitle="请选择所属项目"
/>
```

这样子列表显示的内容为列表名称（name）,绑定的值为项目ID（id）