<template>
	<div align="center" :style="{'border-bottom': showBorder ? '1px solid #F4F4F4' : 'none'}">
		<van-field v-bind="$attrs" v-model="textValue" :disabled="this.$attrs.disabled"
			:clickable="!this.$attrs.disabled" :is-link="!this.$attrs.disabled" readonly @click="showOptions"
			:required="required" />

		<!-- 选择器 -->
		<van-popup v-model:show="showPicker" closeable position="bottom" class="dialog">
			<van-picker :title="optionsTitle" :model-value="pickerValue" :columns="options" @cancel="showPicker = false"
				@confirm="onConfirm" />
			<p class="dia_title">{{ optionsTitle }}</p>
			<div class="content">
				<van-radio-group v-model="chooseType">
					<van-cell-group :border="false">
						<div v-for="(item, index) in options" :key="index">
							<van-cell clickable @click="choosePayType(item)">
								<template #title>
									<span>{{item.name}}</span>
								</template>
								<template #right-icon>
									<van-radio :name="item.value">
										<template #icon="props">
											<svg-icon :iconName="props.checked ? 'selected' : 'unSelected'"
												:color="color" />
										</template>
									</van-radio>
								</template>
							</van-cell>
						</div>
					</van-cell-group>
				</van-radio-group>
			</div>
			<div class="footer">
				<van-button class="submit" style="margin: .16rem 0"
					@click.prevent="handleSelectOption()">确定</van-button>
			</div>
		</van-popup>
	</div>
</template>

<script>
	import {
		showToast
	} from 'vant'
	import {
		getValueByKeyFromURL,
		getAllBgColor
	} from '../../utils/tools.js'

	export default {
		name: 'CxFilePicker',
		inheritAttrs: false, // 阻止父组件属性合并到子组件根元素上
		props: {
			value: {
				type: [String, Number],
				default: ''
			},
			// 面板选项列表
			options: {
				type: Array,
				default () {
					return []
				}
			},
			// 数据选项对应的标签和值的key
			keyMap: {
				type: Array,
				default () {
					return ['text', 'value']
				}
			},
			// 对应actionSheet的description属性
			optionsTitle: {
				type: String,
				default: ''
			},
			required: {
				type: Boolean,
				default: false
			},
			showBorder: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				chooseType: "",
				textValue: "",
				showPicker: false,
			}
		},
		mounted() {
			this.chooseType = this.value
			const obj = this.options.find(item => item[this.keyMap[1]] == this.value)
			this.textValue = obj ? obj[this.keyMap[0]] : ''
		},
		computed: {
			// actionSheet组件的actions属性
			actions() {
				return this.options.map(option => {
					return {
						...option,
						name: option[this.keyMap[0]]
					}
				})
			},
			bgColor() {
				return getBgColor(getValueByKeyFromURL(location.href, 'productName') || '')
			},
			color() {
				return getAllBgColor(getValueByKeyFromURL(location.href, 'productName') || '').valColor
			},
		},
		watch: {
			'value': function(val) {
				this.chooseType = val
				const obj = this.options.find(item => item[this.keyMap[1]] == val)
				this.textValue = obj ? obj[this.keyMap[0]] : ''
			}
		},
		methods: {
			// 显示选项弹窗
			showOptions() {
				if (!this.$attrs.disabled) {
					this.chooseType = this.value
					this.showPicker = true
				}
			},
			// 选择某一项时，返回该项的value（值），并隐藏actionSheet组件Z
			onConfirm({ selectedValues, selectedOptions }) {
				if (this.chooseType || this.chooseType === 0) {
					this.$emit('update:value', this.chooseType)
					const obj = this.options.find(item => item[this.keyMap[1]] == this.chooseType)
					this.textValue = obj ? obj[this.keyMap[0]] : ''
					this.showPicker = false
				} else {
					showToast('请选择')
				}

			},
			choosePayType(item) {
				this.chooseType = item[this.keyMap[1]]
			}
		}
	}
</script>
<style lang="less" scoped>
	.dialog {
		max-height: 80vh;
		background: #FFFFFF;
		box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.04);
		border-radius: 48rpx 48rpx 0 0;
		display: flex;
		flex-direction: column;

		.dia_title {
			font-weight: bold;
			font-size: 20px;
			color: #222222;
			line-height: 32px;
			position: relative;
			text-align: center;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-top: 40rpx;
		}

		/deep/.van-popup__close-icon--top-right {
			top: 52rpx;
			height: 32rpx;
			color: #666666
		}

		.content {
			flex: 1;
			overflow: auto;

			/deep/.van-cell {
				padding: 32rpx;
				margin: .12rem .16rem 0;
				background: #FAFAFA;
				border-radius: .12rem;
				width: auto;

				.van-cell__title,
				.van-cell__value {
					text-align: left;
				}

				.van-radio__icon {
					width: .26rem;
					height: .26rem;
					font-size: .24rem;
				}
			}

			/deep/.van-cell:last-of-type {
				margin-bottom: .12rem
			}

			.svg-icon {
				width: .24rem;
				height: .24rem;
			}
		}
	}

	:deep(.van-field .van-cell__right-icon) {
		position: relative;
		bottom: auto;
		right: auto;
	}

	:deep(.van-cell::after) {
		border-bottom: none
	}
</style>