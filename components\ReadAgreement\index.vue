<template>
    <view class="read-container">
        <!-- 阅读协议模块 -->
        <view class="checkbox-box">
            <view class="checkbox">
                <van-checkbox v-model="checked" :checked-color="color" icon-size="18px" @change="handleChange"
                    shape="round">
				</van-checkbox>
            </view>

            <view class="text">
                <view>我已阅读并同意</view>
                <zcx-agreement-box :productName="productName" :isInline="true" :color="color"
                    :cardTypeCode="cardTypeCode" />
            </view>
        </view>
    </view>
</template>

<script>
import ZcxAgreementBox from '@/components/ZcxAgreementBox/index.vue'
export default {
    name: 'readAgreement',
    components: {
        ZcxAgreementBox,
    },
    props: {
        // 是否确认已阅读
        confirmRead: {
            type: Boolean,
            default: false
        },
        productName: {
            type: String,
            default: null
        },
        color: {
            type: String,
            default: ''
        },
        cardTypeCode: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            checked: false, // 是否已确认阅读
        }
    },
    watch: {
        confirmRead(val) {
            this.checked = val
        },
    },
    mounted() {
        console.log("read>>>>>>", this.cardTypeCode, this.productName)
    },
    methods: {
        // 把勾选状态同步到父组件
        handleChange(event) {
            this.$emit('update:confirmRead', event)
        },
    }
}
</script>

<style lang="less" scoped>
.read-container {
    .checkbox-box {
        background: transparent;
        // padding: 0.15rem;
        display: flex;
        font-size: 24rpx;
        line-height: 36rpx;
        color: #222222;
        align-items: center;

        .checkbox {
            margin-right: 12rpx;

            :deep(.van-checkbox__icon .van-icon) {
                border-radius: .04rem
            }
        }
    }
}
</style>

<style lang="less">
// 强制阅读协议弹窗（readAgreement组件内弹窗样式）
#indexMain .van-popup {
    .readPopup {
        height: 100%;
        display: flex;
        flex-direction: column;

        .title {
            height: 0.6rem;
            box-sizing: border-box;
            line-height: 0.28rem;
            padding: 0.16rem;
            text-align: center;
            font-weight: 400;
            color: #333333;
            font-size: 0.16rem;
            position: relative;

            span {
                display: block;
                text-align: left;
                position: absolute;
                top: 0.16rem;
                left: 0.16rem;
                font-weight: 400;
                color: #999999;
                font-size: 0.14rem;
            }

            h2 {
                font-weight: 400;
                font-size: 16px;
                color: #333333;
                margin: 0;
            }
        }

        // 步骤样式
        .van-step {
            display: flex;
            flex-direction: column-reverse;
            align-items: center;
            color: #A9A9A9;

            &.van-step--finish {
                .van-step__title {
                    color: var(--primaryColor);
                }

                .van-step__circle-container {
                    span {
                        color: #fff;
                        background: var(--primaryColor);
                        border: 1px solid var(--primaryColor);
                    }
                }
            }

            .van-step__line {
                top: 0.15rem;
                left: 50%;
            }

            .van-step__circle-container,
            &:last-child {
                position: static;
            }

            .van-step__circle-container {
                transform: translateY(0);

                span {
                    display: block;
                    width: 0.3rem;
                    height: 0.3rem;
                    line-height: 0.3rem;
                    border-radius: 50%;
                    border: 1px solid #cccccc;
                    text-align: center;
                    font-size: 0.16rem;
                    font-weight: 500;
                }
            }

            .van-step__title {
                transform: translateX(0);
                margin-left: 0;
                font-size: 0.12rem;
            }
        }

        .agreementBox {
            flex: 1;
            overflow-y: scroll;
            margin-bottom: 0.1rem;
        }

        .confirm-btn {
            height: 0.48rem;
            border-radius: 0;
            font-size: 0.17rem;
            font-weight: 500;
        }
    }
}
</style>
