<template>
  <div v-if="isExternal" :style="styleExternalIcon" class="svg-external-icon svg-icon" />
  <svg v-else :class="svgClass" aria-hidden="true">
    <use :xlink:href="iconContext" />
  </svg>
</template>

<script lang="ts" setup>
import { computed } from 'vue';

interface Props {
  name: string;
  color: { type: String, default: 'currentColor' };
  size: { type: [String, Number], default: '1em' };
  className?: string; // 自定义类名
}

const props = defineProps<Props>();

const isExternal = computed(() => {
  return /^(https?:|mailto:|tel:)/.test(props.name);
});

// 本地图标
const iconContext = computed(() => {
  return `#icon-${props.name}`;
});

// 本地svg
const svgClass = computed(() => {
  console.log("props.className", props.className)
  return props.className ? `svg-icon ${props.className}` : 'svg-icon';
});

// 外部svg
const styleExternalIcon = computed(() => {
  return {
    mask: `url(${props.name}) no-repeat 50% 50%`,
    '-webkit-mask': `url(${props.name}) no-repeat 50% 50%`,
  };
});

</script>

<style scoped>
.svg-icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}

.svg-external-icon {
  background-color: currentColor;
  mask-size: cover !important;
  display: inline-block;
}
</style>
