<template>
  <div class="agreement-box" :class="isInline ? 'inline' : 'flex'">
    <ul>
      <li v-for="(item) of agreements" :key="item.key" @click="show(item)">
        {{ isText ? item.name : '《' + item.name + '》' }}
      </li>
    </ul>

    <!-- pdf、协议弹窗 -->
    <zcx-agreement-popup v-model="pdfShow" :colseScrollTop="true">
      <div class="readPopup">
        <div class="title">
          <h2>{{ currentName }}</h2>
        </div>
        <div class="agreement" ref="main">
          <iframe v-if="!isPDF" :src="currentComponent" class="htmlBox" frameborder="0"></iframe>
		  <wx-open-data v-else class="pdfBox" type="pdf" :src="currentComponent"></wx-open-data>
        </div>
      </div>
    </zcx-agreement-popup>
  </div>
</template>

<script>
import ZcxAgreementPopup from '@/components/ZcxAgreementPopup/index.vue'
// 协议文档
import api from '@/api/cityHealth'

export default {
  name: 'ZcxAgreementBox',
  components: {
    ZcxAgreementPopup
  },
  props: {
    // 是否是行内样式布局
    isInline: {
      type: Boolean,
      default: false
    },
    // 是否不带《》
    isText: {
      type: Boolean,
      default: false
    },
    // 区分协议
    typeCode: {
      type: String,
      default: ""
    },
    // 卡种类
    cardTypeCode: {
      type: String,
      default: ""
    },
    // 项目名
    productName: {
      type: String,
      default: ""
    },
  },
  data() {
    return {
      agreements: [],
      agreementList1: [],
      pdfShow: false, // pdf协议弹窗
      currentComponent: '', // 当前组件
      currentName: '', // 当前组件名字
      isPDF: false,
      numPages: '',
    }
  },
  watch: {
    typeCode: {
      immediate: true,
      handler(newValue) {
        console.log('newValue-typeCode', newValue)
        if (newValue) {
          this.getAgreementsList()
        }
      }
    },
    productName: {
      immediate: true,
      handler(newValue) {
        console.log('newValue-productName', newValue, this.productName)
        if (newValue) {
          this.getAgreementsList()
        }
      }
    },
    cardTypeCode: {
      immediate: true,
      handler(newValue) {
        console.log('newValue-cardTypeCode', newValue,this.cardTypeCode)
        // if (newValue) {
        //   this.cardTypeCode = newValue
        //   this.getAgreementsList()
        // }
      }
    },
  },
  computed: {},
  mounted() {
    this.getAgreementsList()
  },
  methods: {
    getAgreementsList() {
      var params = []
      console.log("this.cardTypeCode", this.cardTypeCode, "this.productName", this.productName, "this.typeCode", this.typeCode)
      if (this.cardTypeCode) {
        params = [
          { cardTypeCode: this.cardTypeCode, keys: "sysm" }
        ]
      } else if (this.typeCode) {
        params = [
          { cardTypeCode: this.typeCode, keys: "sysm" },
          { cardTypeCode: '000', keys: "ptfwxy" },
          { cardTypeCode: '000', keys: "ysxy" },
        ]
      } else if (this.productName) { // 惠民保
        params = [
          { cardTypeCode: '000', keys: "ptfwxy" },
          { cardTypeCode: '000', keys: "ysxy" },
        ]
      } else {
        params = [
          { cardTypeCode: '000', keys: "ptfwxy" },
          { cardTypeCode: '000', keys: "ysxy" },
        ]
      }
      api.queryAgreementListPost(params).then((res) => {
        if (res.result) {
          this.agreements = res.result
        }
      })
    },
    // 显示协议内容
    show(item) {
      this.currentName = item.name
      this.currentComponent = item.url
      const pathname = new URL(item.url).pathname
      this.isPDF = pathname.split('.').pop() == 'pdf'
      console.log("this.isPDF", this.isPDF)
      if (this.isPDF) {
        this.previewPdf(item)
      } else {
        this.pdfShow = true
      }
    },
    previewPdf(item) {
      this.currentComponent = pdf.createLoadingTask(item.url)
      this.currentComponent.promise.then(pdf => {
        this.$nextTick(() => {
          this.numPages = pdf.numPages; // pdf总页数
          console.log("this.numPages", this.numPages)
          this.pdfShow = true
        });
      }).catch(() => {
        setTimeout(() => {
          this.$toast("请在手机微信端打开")
        }, 1500);
      })
    },
  },
}
</script>

<style lang="less" scoped>
.agreement-box {
  text-indent: 0;

  &.flex {
    ul {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      font-size: 0.12rem;
      padding: 0 0.15rem;
    }
  }

  &.inline {
    display: inline;

    ul {
      display: inline;

      li {
        display: inline;
      }
    }
  }

  .readPopup {
    height: 100%;
    display: flex;
    flex-direction: column;

    .title {
      height: 0.6rem;
      flex: 1;
      box-sizing: border-box;
      line-height: 0.28rem;
      padding: 0.16rem;
      text-align: center;
      font-weight: 400;
      color: #333333;
      font-size: 0.16rem;
      position: relative;

      h2 {
        font-weight: bold;
        font-size: 16px;
        color: #333333;
        margin: 0;
      }
    }

    .agreement {
      width: 3.75rem;
      height: 6.4rem;
      overflow-y: scroll;
      overflow-x: hidden;
      display: flex;
      flex-direction: column;
      align-items: center;

      .htmlBox {
        width: 100%;
        height: 100%;
      }

      .pdfBox {
        height: 100%;
        min-height: 6.4rem;
      }
    }

    .agreementBox {
      flex: 1;
      overflow-y: scroll;
      margin-bottom: 0.1rem;
    }
  }
}
</style>