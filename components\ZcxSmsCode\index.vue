<template>
	<span @click="querySmsCode()" class="SmsCodeBtn" :style="{ color }">
		{{ smsText }}
	</span>
</template>

<script>
	// import api from '../../api/global.js'
	import { showToast } from 'vant'
	import {
		getValueByKeyFromURL
	} from '../../utils/tools'
	export default {
		name: 'ZcxSmsCode',
		data() {
			return {
				smsText: '获取验证码', // 验证码内容
				lock: false // 是否已发送，锁定60秒
			}
		},
		props: {
			// 手机号
			phoneNo: {
				type: [Number, String],
				default: ''
			},
			// 是否显示校验手机号的提示（是否输入数据值，格式是否正确）
			showErrorMsg: {
				type: Boolean,
				default: true
			},
			// 发送短信的时间间隔（即：发送后页面显示的倒计时时长，默认60秒）
			interval: {
				type: Number,
				default: 60
			},
			// 字体颜色
			color: {
				type: String,
				default: '#1669E3'
			}
		},
		methods: {
			// 获取验证码
			querySmsCode() {
				// 如果已被锁定，则不做操作
				if (this.lock) {
					return false
				}

				// 校验手机号
				if (!this.phoneNo) {
					this.showErrorMsg && showToast('请先输入手机号')
					return false
				}
				if (!/^[1][0-9][0-9]{9}$/.test(this.phoneNo)) {
					this.showErrorMsg && showToast('请输入正确的手机号')
					return false
				}
				var name = "health"
				let productName = getValueByKeyFromURL(document.location, "productName")
				console.log("productName:", productName)
				if (this.$route.query.source) {
					name = this.$route.query.source
				}
				// 调用接口，发送短信验证码
				api.querySmsCode({
					phoneNo: this.phoneNo,
					source: name
				}).then(() => {
					showToast('短信已发送');
					this.lock = true // 锁定获取验证码按钮，不可再点击

					// 60秒倒计时
					let time = this.interval
					this.smsText = time + 's重新发送'

					let sendTimer = setInterval(() => {
						time--
						this.smsText = time + 's重新发送'

						if (time < 0) {
							clearInterval(sendTimer)
							this.lock = false
							this.smsText = '获取验证码'
						}
					}, 1000)

				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.SmsCodeBtn {
		cursor: pointer;
		font-size: 28rpx;
		line-height: 40rpx;
	}
</style>