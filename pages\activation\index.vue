<template>
	<view class="activation">
		<!-- 主容器 -->
		<view class="main">
			<view style="flex:1; overflow: auto; border-radius: 16rpx;">
				<!-- 卡密 -->
				<view class="cardSecret_info">
					<text class="title but_round">请填写卡密</text>
					<van-form class="cardSecret_box">
						<van-field v-model="cardSecret" placeholder="请输入卡背面涂层下的卡密" clearable
							:rules="validateRules.cardSecretRules" @blur="check" :disabled="cardDisabled">
							<template #extra>
								<text style="color: #1669E3; font-size: 24rpx">*卡密区分大小写</text>
							</template>
						</van-field>
					</van-form>
				</view>

				<!-- 权益人信息 -->
				<view class="card_info">
					<text class="title but_round">绑定权益人</text>
					<van-form ref="personInfo">
						<van-field v-model="bindName" label="会员姓名" placeholder="请输入权益人真实姓名" clearable
							:rules="validateRules.nameRules" />

						<cx-field-select label="证件类型" placeholder="请选择证件类型" optionsTitle="请选择证件类型" v-model="bindIdType"
							:options="idTypeList_TYQNK" :rules="validateRules.idTypeRules"
							style="border-bottom: 1px solid rgba(235, 237, 240, 0.5)" />

						<van-field v-model="bindIdNum" label="证件号码" placeholder="请输入证件号码" clearable
							:rules="idNumRules" />

						<cx-field-datetime v-if="bindIdType != 1" label="出生日期" placeholder="请选择出生日期" v-model="birthday"
							:min-date="minDate" :max-date="maxDate" :rules="bindIdType != 1 ? birthdayRules : []" />

						<cx-field-select v-if="bindIdType != 1" label="性别" placeholder="请选择性别" v-model="sex"
							:options="sexList" optionsTitle="请选择性别"
							:rules="bindIdType != 1 ? validateRules.sexRules : []"
							style="border-bottom: 1px solid rgba(235, 237, 240, 0.5)" />

						<cx-field-area label="所在城市" placeholder="请选择您的所在城市" pompTitle="请选择您的所在城市" v-model="bindCity" />

						<van-field v-model="bindPhoneNo" label="手机号" placeholder="请输入手机号" clearable :maxlength="11"
							:rules="validateRules.phoneNoRules" :disabled="telDisabled" />

						<van-field v-model="bindCode" label="验证码" placeholder="请输入验证码" clearable :maxlength="6"
							:rules="validateRules.smsCodeRules">
							<template #button>
								<zcx-sms-code :phoneNo="bindPhoneNo" color="#ffffff" />
							</template>
						</van-field>
					</van-form>
				</view>

				<!-- 代理人 -->
				<view class="agent_box">
					<van-form class="form_box" ref="agentInfo">
						<van-field v-model="agent" label="代理人" :placeholder="isRbs ? '请输入代理人工号' : '请输入代理人工号（选填）'"
							:rules="isRbs ? validateRules.agentRules : []" clearable />
					</van-form>
				</view>

				<text class="content" v-if="showCont">{{ content }}</text>
			</view>
		</view>

		<!-- 阅读协议模块 -->
		<view class="module">
			<read-agreement :confirm-read.sync="confirmRead" :code.sync="cardTypeCode" />
		</view>

		<!-- 底部按钮 -->
		<view class="footer">
			<van-button :disabled="!complete" type="primary" class="submit" @click="activate">
				完成激活并绑定权益人
			</van-button>
		</view>
	</view>

</template>

<script setup>
	import { ref } from 'vue'
	import ZcxSmsCode from '../../components/ZcxSmsCode/index.vue'
	import CxFieldSelect from '../../components/CxFieldSelect/index.vue'
	import { idTypeList_TYQNK } from '../../assets/json/dictCode'
	import { getValueByKeyFromURL } from '../../utils/tools'
	import validateRules from '../../utils/validateRules'
	import { computed } from 'vue'
	import api from '../../api/cityHealth.js'

    // 数据绑定-基础数据获取
	const cardTypeCode = ref(getValueByKeyFromURL(location.href, 'productName') || '') // 卡种号
	// 数据绑定-表单提交
	const cardSecret = ref('') // 卡密
	const bindName = ref('') // 激活人
	const bindIdType = ref('') // 证件类型
	const bindIdNum = ref('') // 身份证号
	const birthday = ref('') // 生日
	const sex = ref('') // 性别
	const bindCity = ref('') // 城市
	const bindPhoneNo = ref('') // 手机号
	const bindCode = ref('') // 验证码
	const agent = ref('') // 代理人工号
	// 数据设定
	const minDate = new Date(1900, 0, 1) // 最小可选日期
	const maxDate = new Date() // 最大可选日期
	// 数据绑定-配置项
	const content = ref('') // 年龄提示时间区间
	const confirmRead = ref(false) // 阅读协议是否勾选
	// 判断是否全部填写
	const complete = computed(() => {
	    return confirmRead.value && cardSecret.value && bindName.value && bindIdNum.value
	})
	// 判断是否必填代理人
	const isRbs = computed(() => {
	    let val = false
	    if (cardTypeCode.value === '144' || cardTypeCode.value === '145') {
			val = true
	    }
	    return val
	})
	// 判断是否展示年龄限制提示
	const showCont = computed(() => {
	    let val = true
	    if (cardTypeCode.value === '144' || cardTypeCode.value === '145') {
			val = false
	    }
	    return val
	})
	
	const activate = async () => {
		const respont = await api.check({ cardSecret: this.cardSecret, cardTypeCode: this.cardTypeCode }, { codeAllPass: true, noLoading: true })
	}
	
</script>

<style lang="less" scoped>
	.activation {
		background: linear-gradient(360deg, #FFFFFF 0%, #ECF6FF 100%);
		height: 100vh;
		display: flex;
		flex-direction: column;

		.banner_img {
			position: relative;
		}

		.top_button {
			width: 8rpx;
			height: 39rpx;
			position: absolute;
			top: 37%;
			left: 77%;
			background-color: #e3681600;
			color: #e3681600;
		}

		.main {
			flex: 1;
			padding: 24rpx 24rpx 0;
			overflow-y: auto;
			background: rgba(255, 255, 255, 0.5);
			box-shadow: 0rpx 4rpx 16rpx 0rpx rgba(0, 0, 0, 0.04);
			border-radius: 48rpx 48rpx 0rpx 0rpx;
			border: 2rpx solid #FFFFFF;
			margin-top: 32rpx;
			display: flex;
		}

		.cardSecret_info {
			background: #FFFFFF;
			box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(0, 0, 0, 0.06);
			border-radius: 32rpx;
			border: 2rpx solid #FFFFFF;
			padding: 32rpx 32rpx 40rpx;
			margin: 0 auto 24rpx;

			.title {
				font-family: "ShuHei-B";
				font-size: 44rpx;
				color: #222222;
				line-height: 60rpx;
			}

			.cardSecret_box {
				background: #ffffff;
				border-radius: 16rpx;
				border: 2rpx solid #EEEEEE;
				margin-top: 32rpx;
			}

			:deep(.van-form) {
				padding: 0 32rpx;
			}
		}

		// 代理人
		.agent_box {
			.form_box {
				background: #FFFFFF;
				box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(0,0,0,0.06);
				border-radius: 32rpx;
				border: 2rpx solid #FFFFFF;
			}

			:deep(.van-form) {
				padding: 0 32rpx;
			}
		}


		.card_info {
			padding: 32rpx 32rpx 0;
			background: #FFFFFF;
			box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(0,0,0,0.06);
			border-radius: 32rpx;
			border: 2rpx solid #FFFFFF;
			margin: 0 auto 24rpx;

			.title {
				font-family: "ShuHei-B";
				font-size: 44rpx;
				color: #222222;
				line-height: 60rpx;
				display: inline-block;
			}
		}

		.content {
			font-size: 24rpx;
			color: #999999;
			line-height: 32rpx;
			margin: 48rpx auto;
			text-align: center;
		}

		.dialog {
			background: transparent;

			.top {
				width: 40rpx;
				background: #fff;
				border-radius: 0.16rem;
				box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.04);
				text-align: center;
				margin: 0 auto;

				.title {
					padding: 0.24rem 0 0.16rem;
					text-align: center;
					font-size: 0.2rem;
					font-weight: 500;
					line-height: 0.28rem;
				}

				.text {
					font-size: 0.16rem;
					line-height: 0.24rem;
					padding: 0 0.24rem;
					text-align: center;
				}

				img {
					width: 1.78rem;
					margin-bottom: 0.24rem;
				}
			}

			.close {
				width: 0.32rem;
				margin: 0.16rem 1.71rem;
			}
		}

		.module {
			display: flex;
			background: #fff;
			padding: 0.13rem 0.16rem;
			box-shadow: 0px -2px 8px 0px rgba(0, 0, 0, 0.04);
		}

		:deep(.van-form) {

			.van-field {
				display: flex;
				align-items: center;
				padding: 32rpx 0;
			}

			.van-field .van-field__label {
				width: 112rpx;
				font-size: 28rpx;
				color: #666666;
				line-height: 40rpx;
				margin-right: 64rpx;
			}

			.van-cell {
				align-items: center;
			}
			
			.van-field__control {
				font-size: 27rpx;
				line-height: 40rpx;
			}
			
			input::placeholder {
				color: #bbbbbb;
				font-size: 16rpx;
				font-weight: 400;
			}
			
			.van-field__error-message {
				position: absolute;
				top: 31rpx;
			}

			.van-cell::after {
				border-bottom: 0;
			}

			.van-field__button {
				background: #FFFFFF;
				border-radius: 32rpx;
				padding: 14rpx 32rpx;
				line-height: 36rpx;
			}
		}

		:deep(.van-button--disabled) {
			background: #cccccc !important;
			border: none !important;
		}

		:deep(.van-action-sheet__header) {
			padding: 0.24rem 0 0.16rem;
			font-size: 0.2rem;
			font-weight: bold;
			color: #222222;
			line-height: 0.24rem;
		}

		:deep(.van-action-sheet__close) {
			top: 0.24rem;
		}

		.termContent {
			padding: 0 0.16rem 0.16rem;
			margin: 0;
			font-size: 0.14rem;
			color: #666666;
			line-height: 0.22rem;
			text-indent: 0.28rem;
		}

		.termBtnBox {
			border: 1px solid #f4f4f4;
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 0.16rem 0;

			.iKnow {
				width: 3.43rem;
				height: 0.48rem;
				background: #1669e3;
				border-radius: 0.24rem;
				font-size: 0.18rem;
				font-weight: 500;
			}
		}

		:deep(.radio-block) {
			box-shadow: 0px -2px 8px 0px rgba(0, 0, 0, 0.04);

			.van-field {
				padding: 0.16rem;
			}

			.van-field .van-field__label {
				width: 2rem;
			}

			.van-field__control:read-only {
				display: block;
			}

			.van-radio-group--horizontal {
				justify-content: end;
			}

			.van-radio__icon {
				height: auto;
			}

			.img-icon {
				width: 0.2rem;
			}
		}

		.messDialog {
			width: 3.11rem;
			background: #ffffff;
			border-radius: 0.16rem;
			box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.04);

			.title {
				margin: 0.24rem auto 0.16rem;
				text-align: center;
				font-size: 0.2rem;
				font-weight: 500;
				line-height: 0.28rem;

			}

			.text {
				font-size: 0.16rem;
				line-height: 0.24rem;
				padding: 0 0.18rem;
				text-align: center;
			}

			.iKnow {
				width: 2.63rem;
				height: 0.48rem;
				border-radius: 0.24rem;
				margin: 0.48rem 0.24rem 0.24rem;
				font-size: .2rem;

			}

			.healthIKnow {
				width: 2.63rem;
				height: 0.48rem;
				border-radius: 0.24rem;
				margin: 0.48rem 0.24rem 0.24rem;
				background: #F1F4F5;
				border: none;
				font-size: .2rem;
				color: #444444;

			}
		}
	}
</style>