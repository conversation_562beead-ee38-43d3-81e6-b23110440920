<template>
	<view class="login" v-show="showLogin">
		<img :src="bannerSrc" class="login_logo" />
		<view class="main">
			<img src="../../assets/images/login/login_icon.png" class="login_icon">
			<van-form ref="login">
				<van-field v-model="phoneNo" name="productName" placeholder="请输入手机号" clearable :maxlength="11"
					:rules="validateRules.phoneNoRules" />
				<van-field v-model="smsCode" name="productName" placeholder="请输入验证码" clearable :maxlength="6"
					:rules="validateRules.smsCodeRules">
					<template #button class="smsCode">
						<zcx-sms-code :phoneNo="phoneNo" :color="color" />
					</template>
				</van-field>

				<van-button class="btn" @click="agreeCheck">登录</van-button>

				<read-agreement class="module" :confirmRead.sync="confirmRead" :productName="productName"
					:color="color" />
			</van-form>
		</view>


		<van-popup v-model="popupShow" class="dialog" position="bottom" closeable>
			<p class="dialog_title">服务协议及隐私保护</p>
			<view class="dialog_agreement">
				<span>为了更好的保护您的合法权益，请您阅读并同意以下协议</span>
				<zcx-agreement-box :productName="productName" :isInline="true" :color="color" />
			</view>
			<view class="btn_list">
				<van-button class="unAgree" :style="{ border: '2rpx solid ' + color, color: color }"
					@click="popupShow = false">不同意</van-button>
				<van-button class="agree" @click="login">同意</van-button>
			</view>
		</van-popup>
	</view>
</template>

<script setup>
	import {
		ref,
		computed,
		onMounted
	} from 'vue'
	import {
		Field,
		Form,
		Button
	} from 'vant'

	import {
		useRoute
	} from 'vue-router'

	import ZcxSmsCode from '../../components/ZcxSmsCode/index.vue'
	import readAgreement from '../../components/ReadAgreement/index.vue'
	import ZcxAgreementBox from '../../components/ZcxAgreementBox/index.vue'
	import validateRules from '../../utils/validateRules'
	import {
		getValueByKeyFromURL
	} from '../../utils/tools'

	// 使用 vue-router 的 route 对象
	const route = useRoute()

	// 定义数据
	const productName = ref('')
	const cardTypeCode = ref('')
	const showLogin = ref(true) // 页面展示
	const phoneNo = ref('')
	const smsCode = ref('')
	const confirmRead = ref(false) // 确认已阅读协议
	const popupShow = ref(false)
	const registerSource = ref(route.query.registerSource?.toString() || '')
	const routing = ref(route.query.routing?.toString() || '')
	const extraParams = ref(route.query.extraParams?.toString() || '')
	const returnUrl = ref('')
	const loginForm = ref()

	// 初始化产品名称
	if (route.query.returnUrl && getValueByKeyFromURL(decodeURIComponent(route.query.returnUrl.toString()),
			'productName')) {
		productName.value = getValueByKeyFromURL(decodeURIComponent(route.query.returnUrl.toString()), 'productName') || ''
	} else if (getValueByKeyFromURL(location.href, 'productName')) {
		productName.value = getValueByKeyFromURL(location.href, 'productName') || ''
	} else if (getValueByKeyFromURL(location.href, 'cardTypeCode')) {
		cardTypeCode.value = getValueByKeyFromURL(location.href, 'cardTypeCode') || ''
		console.log("cardTypeCode----------------", getValueByKeyFromURL(location.href, 'cardTypeCode'), cardTypeCode
			.value)
	} else {
		productName.value = 'null'
	}
	returnUrl.value = route.query.returnUrl?.toString() || ''

	const bannerSrc = computed(() => {
		// if (!productName.value) {
		// 	return new URL("../../assets/images/login/login_null.png", import.meta.url).href
		// } else {
		// 	// return new URL(`../assets/images/login/login_${productName.value}.png`, import.meta.url).href
		// 	return new URL("../../assets/images/login/login_null.png", import.meta.url).href
		// }
	})
</script>

<style lang="less" scoped>
	.login {
		height: 100vh;
		display: flex;
		flex-direction: column;

		.login_logo {
			width: 750rpx;
		}

		.login_icon {
			position: absolute;
			width: 188rpx;
			top: -82rpx;
			left: 48rpx;
		}

		.main {
			flex: 1;
			padding: 80rpx 64rpx;
			margin-top: -40rpx;
			position: relative;
			background: #FFFFFF;
			box-shadow: 0rpx 0rpx 16rpx 0rpx rgba(0, 0, 0, 0.04);
			border-radius: 48rpx 48rpx 0rpx 0rpx;
			border: 2rpx solid #FFFFFF;

			.btn {
				width: 622rpx;
				height: 96rpx;
				background: linear-gradient(90deg, #1669E3 0%, #493EE1 100%);
				box-shadow: 0rpx 4rpx 16rpx 0rpx rgba(22, 105, 227, 0.2), inset 0rpx -4rpx 8rpx 0rpx rgba(106, 155, 227, 0.4), inset 0rpx 4rpx 8rpx 0rpx rgba(26, 50, 182, 0.5);
				border-radius: 48rpx;
				font-size: 40rpx;
				color: #FFFFFF;
				line-height: 64rpx;
				margin-bottom: 48rpx;
				margin-top: 48rpx;
			}
		}

		.dialog {
			width: 100%;
			padding-top: 0.24rem;
			// min-height: 2.32rem;
			background: #ffffff;
			box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.04);
			border-radius: 0.24rem 0.24rem 0px 0px;

			.dialog_title {
				font-size: 0.18rem;
				font-weight: bold;
				line-height: 0.28rem;
				color: #222222;
				text-align: center;
			}

			.dialog_agreement {
				padding: 0.24rem 0.24rem 0.48rem;
			}

			.btn_list {
				width: 100%;
				display: flex;
				justify-content: center;
				align-items: center;
				border-top: 1px solid #f4f4f4;
				padding: 0.16rem 0;

				.unAgree {
					width: 1.2rem;
					height: 0.48rem;
					background: #ffffff;
					border-radius: 0.24rem;
					padding: 0;
					background: #fff;
					font-size: 0.18rem;
					line-height: 0.24rem;
				}

				.agree {
					width: 2.11rem;
					height: 0.48rem;
					color: #fff;
					border-radius: 0.24rem;
					padding: 0;
					font-size: 0.18rem;
					line-height: 0.24rem;
					margin-left: 0.12rem;
				}
			}
		}

		:deep(.van-field) {
			height: 96rpx;
			background: #FAFAFA;
			border-radius: 44rpx;
			display: flex;
			align-items: center;
			padding: 0 48rpx;
			margin: 0 auto 32rpx;
		}

		:deep(.van-field__control) {
			font-size: 32rpx;
			line-height: 40rpx;
		}

		:deep(input::placeholder) {
			color: #bbbbbb;
			font-size: 16rpx;
			font-weight: 400;
		}

		:deep(.van-field__error-message) {
			position: absolute;
			top: 31rpx;
		}

		:deep(.van-cell::after) {
			border-bottom: 0;
		}
		
		:deep(.van-field__button) {
			background: #FFFFFF;
			border-radius: 32rpx;
			padding: 14rpx 32rpx;
			line-height: 36rpx;
		}
	}
</style>