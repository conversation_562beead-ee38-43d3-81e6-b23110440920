/**
 * 身份证15位编码规则：dddddd yymmdd xx p
 * dddddd：地区码
 * yymmdd: 出生年月日
 * xx: 顺序类编码，无法确定
 * p: 性别，奇数为男，偶数为女
 * ----------------------------------------
 * 身份证18位编码规则：dddddd yyyymmdd xxx y
 * dddddd：地区码
 * yyyymmdd: 出生年月日
 * xxx:顺序类编码，无法确定，奇数为男，偶数为女
 * y: 校验码，该位数值可通过前17位计算获得
 *
 * 18位号码加权因子为(从右到左) Wi = [ 7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2,1 ]
 * 验证位 Y = [ 1, 0, 10, 9, 8, 7, 6, 5, 4, 3, 2 ]
 * 校验位计算公式：Y_P = mod( ∑(Ai×Wi),11 )
 * i为身份证号码从右往左数的 2...18 位; Y_P为脚丫校验码所在校验码数组位置
 */

export function idCardValidate(idCard) {
  // 校验时自动删除空格
  // idCard = trim(idCard);

  // 检验身份证格式（包含15和18位，但未校验长度）
  if (!/^[1-9]\d{5}(18|19|20)?\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]?$/.test(idCard)) {
    return false;
  }

  if (idCard.length == 18) {
    if (isTrueValidateCodeBy18IdCard(idCard)) {
      return true;
    } else {
      return false;
    }
  } else if (idCard.length == 15) {
    return true;
  } else {
    return false;
  }
}

/**
 * 判断身份证号码为18位时最后的验证位是否正确
 * @param idCard 身份证号码
 * @return
 */
function isTrueValidateCodeBy18IdCard(idCard) {
  var Wi = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2, 1]; // 加权因子
  var ValideCode = [1, 0, 10, 9, 8, 7, 6, 5, 4, 3, 2]; // 身份证验证位值.10代表X
  var a_idCard = idCard.split(''); // 得到身份证数组
  var sum = 0; // 声明加权求和变量
  if (a_idCard[17].toLowerCase() == 'x') {
    // 如果是小写x是校验失败
    // if(a_idCard[17] == 'x') {
    //   return false
    // }
    a_idCard[17] = 10; // 将最后位为x的验证码替换为10方便后续操作
  }
  for (var i = 0; i < 17; i++) {
    sum += Wi[i] * a_idCard[i]; // 加权求和
  }
  var valCodePosition = sum % 11; // 得到验证码所位置
  if (a_idCard[17] == ValideCode[valCodePosition]) {
    return true;
  } else {
    return false;
  }
}

/**
 * 通过身份证判断是男是女
 * @param idCard 15/18位身份证号码
 * @return 'f female'-女、'm male'-男
 */
export function maleOrFemalByIdCard(idCard) {
  idCard = trim(idCard); // 对身份证号码做处理。包括字符间有空格。
  if (idCard.length == 15) {
    if (idCard.substring(14, 15) % 2 == 0) {
      return 'f';
    } else {
      return 'm';
    }
  } else if (idCard.length == 18) {
    if (idCard.substring(14, 17) % 2 == 0) {
      return 'f';
    } else {
      return 'm';
    }
  } else {
    return null;
  }
}

/**
 * 通过身份证号计算出生日期
 */
export function birthdayByIdCard(idCard, splitStr='-') {
  idCard = trim(idCard); // 对身份证号码做处理。包括字符间有空格。
  let year, month, day

  if (!/^\d+[0-9Xx]?$/.test(idCard)) {
    console.error('身份证号格式不正确')
    return false
  }

  // 从证件号中截取出生年月日
  if (idCard.length == 18) {
    year = idCard.substring(6, 10);
    month = idCard.substring(10, 12);
    day = idCard.substring(12, 14);
  } else if (idCard.length == 15) {
    year = idCard.substring(6, 8);
    year = '19' + year; // 补全4位年份
    month = idCard.substring(8, 10);
    day = idCard.substring(10, 12);
  } else {
    return null;
  }

  // 类型为date格式时，返回Date格式
  // if (type === 'date') {
  //   return new Date(year, parseInt(month) - 1, day);
  // } else if (type === 'string') {
  return `${year}${splitStr}${month}${splitStr}${day}`
    // return new Date(year, parseInt(month)-1, day);
  // }
}

//去掉字符串中所有空格
function trim(str) {
  return str.replace(/\s*/g, '');
}
