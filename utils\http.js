// import axios from 'axios';
import { showToast } from 'vant'
import ENV_CONFIG from '../env.js' // 导入环境配置

// 获取当前环境配置
const currentEnv = process.env.ENV_TYPE || 'development'
console.log("aaaaaaaaaaaaaaaaa", currentEnv)
const baseUrl = ENV_CONFIG[currentEnv].baseUrl

// 请求方法封装
function request(method, url, data, config = {}) {
	let codeAllPass = config.codeAllPass || false
	return new Promise((resolve, reject) => {

		// 准备请求头
		const headers = {
			'X-Pbm-ProjectId': 'PR000001',
			...(config.headers || {})
		}
		// 构建请求配置
		const requestConfig = {
			url: baseUrl + 'gateway' + url,
			method: method.toLowerCase(),
			header: headers,
			timeout: 60000,
			data: method === 'GET' ? undefined : data,
			params: method === 'GET' ? data : undefined,
		}

		uni.request({
			...requestConfig,
			success: (res) => {
				const { statusCode, data } = response
				if (statusCode === 200) {
					if (data.code === '200000' || codeAllPass) {
						resolve(data); // 成功时返回数据
					} else {
						// 统一处理错误结果
						handleErrorResponse(data)
						reject(data)
					}
				} else if (res.statusCode === 401) { // 例如，处理401未授权错误，可能需要重新登录等操作
					handleUnauthorized()
					reject(response)
				} else {
					showToast('服务器错误，请稍后再试')
					reject(response)
				}
			},
			fail: (error) => {
				handleRequestFail(error)
				reject(error)
			}
		});
	});
}

// 错误处理函数
function handleErrorResponse(data) {
	if (data.code === '400001') {
		handleUnauthorized()
	} else {
		showToast(data.message || '操作失败')
	}
}

// 未授权处理
function handleUnauthorized() {
	// 清除用户状态
	try {
		uni.removeStorageSync('token')
		uni.removeStorageSync('userInfo')
	} catch (e) {
		console.error('清除用户状态失败:', e)
	}

	// 跳转登录页
	uni.redirectTo({
		url: '/pages/login/login'
	})

	showToast('登录已过期，请重新登录')
}

// 请求失败处理
function handleRequestFail(error) {
	if (error.errMsg && error.errMsg.includes('request:fail')) {
		showToast('网络连接失败，请检查网络设置')
	} else {
		showToast('请求失败，请稍后再试')
	}
}

export default {
	get: (url, params, config) => request('GET', url, params, config),
	post: (url, data, config) => request('POST', url, data, config),
	put: (url, data, config) => request('PUT', url, data, config),
	delete: (url, params, config) => request('DELETE', url, params, config),
}