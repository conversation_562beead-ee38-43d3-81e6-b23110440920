import {
	idCardValidate,
	maleOrFemalByIdCard,
	birthdayByIdCard
} from './IdCard-Validate'
export {
	idCardValidate,
	maleOrFemalByIdCard,
	birthdayByIdCard
}

// 格式化日期
// export function formatDate(date) {
//   const stringDate = `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`
//   return stringDate
// }

// 格式化日期数据
export function formatDate(dateData, type = 'date') {
	let year = dateData.getFullYear().toString() // 年
	let month = (dateData.getMonth() + 1).toString() // 月
	let date = dateData.getDate().toString() // 日
	let H = dateData.getHours().toString() // 时
	let M = dateData.getMinutes().toString() // 分
	let S = dateData.getSeconds().toString() // 秒

	// 如果是个数，则前补0
	month = beforeFill0(month)
	date = beforeFill0(date)
	H = beforeFill0(H)
	M = beforeFill0(M)
	S = beforeFill0(S)

	let stringDate = ''

	if (type === 'datetime') {
		stringDate = `${year}-${month}-${date} ${H}:${M}:${S}`;
	} else if (type === 'datehour') {
		stringDate = `${year}-${month}-${date} ${H}`;
	} else if (type === 'date') {
		stringDate = `${year}-${month}-${date}`;
	} else if (type === 'year-month') {
		stringDate = `${year}-${month}`;
	} else if (type === 'month-day') {
		stringDate = `${month}-${date}`;
	}

	return stringDate;
}

export function getDay(day, nowDay) {
	var today
	if (nowDay) {
		today = new Date(nowDay.replaceAll('-', '/'));
	} else {
		today = new Date();
	}
	var targetday_milliseconds = today.getTime() + 1000 * 60 * 60 * 24 * day;
	today.setTime(targetday_milliseconds); //注意，这行是关键代码
	var tYear = today.getFullYear();
	var tMonth = today.getMonth();
	var tDate = today.getDate();
	tMonth = doHandleMonth(tMonth + 1);
	tDate = doHandleMonth(tDate);
	return tYear + "-" + tMonth + "-" + tDate;
}

export function doHandleMonth(month) {
	var m = month;
	if (month.toString().length == 1) {
		m = "0" + month;
	}
	return m;
}

// 日期前补0方法
export function beforeFill0(num) {
	if (num < 10) {
		return '0' + num
	} else {
		return num
	}
}

// 通过生日计算年龄
export function ageByBirthday(birthday) {
	// 现在时间及年月日
	const nowDate = new Date()
	const nowYear = nowDate.getFullYear()
	const nowMonth = beforeFill0(nowDate.getMonth() + 1)
	const nowDay = beforeFill0(nowDate.getDate())

	let birthdayDate = birthday // 出生日期
	// 如果生日是字符串，则转成以‘/’分隔的格式后再转成Date。因为ios对其它格式兼容有问题。如：2020-11-10转成2020/11/10
	if (typeof birthday === 'string') {
		birthdayDate = new Date(birthday.replace(/(-|\.)/g, '/'))
	}

	if (!(birthdayDate instanceof Date)) {
		return null
	}

	// 出生日期的年月日
	const birthYear = birthdayDate.getFullYear()
	const birthMonth = beforeFill0(birthdayDate.getMonth() + 1)
	const birthDay = beforeFill0(birthdayDate.getDate())

	// 年份之差
	let yearDiff = nowYear - birthYear

	// 如果今年的生日还未到，则减一年（把月日组合后比较更方便）
	if (
		parseInt(nowMonth.toString() + nowDay.toString()) <
		parseInt(birthMonth.toString() + birthDay.toString())
	) {
		yearDiff -= 1
	}

	return parseInt(yearDiff)
}

// 通过身份证号校验是否未成年
export function isNonageByIdCard(idCard) {
	// 通过身份证计算生日，再计算年龄
	let birthday = birthdayByIdCard(idCard)
	return ageByBirthday(birthday) < 18
}

// 通过身份证号计算年龄
export function isAgeByIdCard(idCard) {
	// 通过身份证计算生日，再计算年龄
	let birthday = birthdayByIdCard(idCard)
	return ageByBirthday(birthday)
}

/**
 * @param {String} text 需要复制的内容
 * @param {Boolean} noToast 是否“不使用默认提示语”
 * @return {Boolean} 复制成功:true或者复制失败:false  执行完函数后，按ctrl + v试试
 */
export function copyText(text, noToast) {
	var textareaEl = document.createElement('textarea')
	textareaEl.setAttribute('readonly', 'readonly') // 防止手机上弹出软键盘
	textareaEl.value = text
	document.body.appendChild(textareaEl)
	textareaEl.select()
	var res = document.execCommand('copy')
	document.body.removeChild(textareaEl)

	// 默认使用默认提示语，noToast为true是不提示
	if (!noToast) {
		if (res) {
			Toast.success('复制成功')
		} else {
			Toast.fail('复制失败')
		}
	}

	return res
}

// 简单深拷贝
export function deepCopy(obj) {
	try {
		return JSON.parse(JSON.stringify(obj))
	} catch (error) {
		console.error('拷贝内容必须是对象')
		return {}
	}
}

/**
 * @title 函数节流
 * @desc 连续触发事件但是在n秒中只执行一次函数。即 2n 秒内执行 2 次... 。节流如字面意思，会稀释函数的执行频率。
 * @param func 函数
 * @param wait 延迟执行毫秒数
 * @param immediate true - 立即执行（时间戳版）， false - 延迟执行（定时器版）
 */
export const throttle = (func, wait = 16, immediate) => {
	if (immediate) {
		var previous = 0;
	} else {
		var timer;
	}
	return function() {
		var context = this; // 注意 this 指向
		var args = arguments; // arguments中存着event
		if (immediate) {
			var now = Date.now();

			if (now - previous > wait) {
				func.apply(context, args);
				previous = now;
			}
		} else {
			if (!timer) {
				timer = setTimeout(() => {
					timer = null;
					func.apply(context, args)
				}, wait)
			}
		}
	}
}



/**
 * @title 函数防抖
 * @desc 短时间内多次触发同一事件，只执行最后一次（非立即执行），或者只执行最开始的一次（立即执行），中间的不执行。
 * @param func 目标函数
 * @param wait 延迟执行毫秒数
 * @param immediate true - 立即执行， false - 延迟执行
 */

export const debounce = (func, wait, immediate) => {
	var timer;
	return function() {
		var context = this; // 注意 this 指向
		var args = arguments; // arguments中存着event

		if (timer) clearTimeout(timer);
		if (immediate) {
			var callNow = !timer;
			timer = setTimeout(function() {
				// 设置为null即下次callNow为true，即可实现下次执行。
				timer = null;
			}, wait);
			if (callNow) func.apply(context, args);
		} else {
			timer = setTimeout(function() {
				func.apply(context, args);
			}, wait)
		}
	}
}

/**
 * 【获取指定URL中对应key的参数值】
 */

export function getValueByKeyFromURL(url, key) {
	const regex = new RegExp('(^|&|\\?)' + key + '=([^&=#]*)', 'i')
	const result = regex.exec(url)
	if (result != null) {
		return result[2]
	} else {
		return null
	}
}

/**
 * 递归解码URL
 */
export function decodeURLComponentFinal(url) {
	if (url && url.indexOf('%') != -1) {
		decodeURLComponentFinal(decodeURIComponent(url));
	}
	return url;
}

/**
 * 【获取当前URL中对应key的参数值】
 */

export function getParameter(key) {
	var reg = new RegExp('(^|&)' + key + '=([^&]*)(&|$)', 'i') //构建一个含有目标参数的正则表达式对象
	/*
	 * 正则解析：
	 * (^|&) ：表示匹配以参数名字符串开头或者&字符
	 * ([^&]*)：表示匹配除了&之外的字符0次或多次 相当于 {0,}
	 * (&|$) ：表示匹配以字符串结尾或者&字符
	 */
	var result = window.location.search.substr(1).match(reg) //匹配目标参数
	if (result != null) {
		return result[2] //返回参数值
	} else {
		return null
	}
}


/*
 * 掩码字符串
 * str：掩码字符串；replaceStr：掩码部分的替换字符串；beforeNum：前面保留字符明文的个数，afterNum：后面保留字符明文的个数
 */
export function maskString(str, beforeNum = 3, afterNum, replaceStr = '*') {
	if (!str) return ''

	// 设置后面保留数，如果没有则和前面保留数一
	afterNum = afterNum || beforeNum

	// 如果前后个数之和超过字符串总长度，则全部掩码
	if (beforeNum + afterNum >= str.length) {
		// return replaceStr.repeat(str.length)
		return str.slice(0, beforeNum) + replaceStr
	}
	// num = str.length > num * 2 ? num : Math.ceil(str.length/2) - 1

	const beforeStr = str.slice(0, beforeNum) // 前面明文内容
	const afterStr = afterNum === 0 ? '' : str.slice(-afterNum) // 后面明文内容（str.slice(-0)是截取所有）
	let middleNum = str.length - (beforeNum + afterNum) // 中间掩码字符串个数
	const middleStr = middleNum > 1 ? replaceStr.repeat(middleNum) : replaceStr; // 中间掩码字符串内容
	return beforeStr + middleStr + afterStr
}

export const moneyFormat = (money) => {
	const numArr = money.toString().split('.')
	let integer = numArr[0] || '' // 整数
	let decimal = numArr[1] || '' // 小数

	// 整数部分加千分位
	integer = integer.replace(/(\d)(?=(\d{3})+$)/g, $1 => $1 + ",")
	// return `${integer}.<span class="decimal">${decimal}</span>`
	if (integer && decimal) {
		return `${integer}.${decimal}`
	} else if (integer) {
		return `${integer}`
	}
}

export const integerFormat = (num) => {
	const numArr = num.toString().split('.')
	let integer = numArr[0] || '' // 整数
	// let decimal = numArr[1] || '' // 小数

	return integer
}

export const pipFormat = (val) => {
	return val.replace(/\s*/g, "")
}

// 排序数组递归
export function unique(arr) {
	const res = new Map()
	return arr.filter((a) => !res.has(a) && res.set(a, 1))
}
export function groupArr(arr, k) {
	let allGroupName = arr.map(item => {
		return item[k]
	})
	let typeList = unique(
		allGroupName
	);
	let newArr = []
	typeList.forEach(ele => {
		let obj = {};
		obj.list = [];
		obj.list = arr.filter(item => ele == item[k])
		obj.name = ele
		newArr.push(obj)
	})
	return newArr
}

// 日期横杠转换
export function interchangeZh(time) {
	if (time) {
		var str = time.split(" ")[0] + " "
		str = str.replace(/(\/|-)/, '年');
		str = str.replace(/(\/|-)/, '月');
		str = str.replace(' ', '日');
		return str
	} else {
		return ""
	}
}

// 不同项目不同色值
export function getAllBgColor(name) {
	// 字体颜色
	var valColor = ""
	// 权益颜色
	var valRigColor = ''
	// 页面背景色
	var valBgColor = ''
	switch (name) {
		case 'ah2025':
			valColor = '#1669E3'
			valRigColor = '#1669E3'
			valBgColor = 'linear-gradient(360deg, #FFFFFF 0%, #ECF6FF 100%)'
			break
		default:
			valColor = '#1669E3'
			valRigColor = '#1669E3'
			valBgColor = 'linear-gradient(360deg, #FFFFFF 0%, #FFF9F6 100%)'
	}
	return {
		valColor,
		valRigColor,
		valBgColor
	}
}
// 按钮颜色、按钮背景色
export function getButtonColor(name) {
	var valBgColor = ''
	var valShaColor = ''
	switch (name) {
		case 'ah2025':
			valBgColor = '#0D85E8';
			valShaColor =
				'0px 2px 8px 0px rgba(5,38,204,0.2), inset 0px -2px 4px 0px rgba(42,73,226,0.4), inset 0px 2px 4px 0px rgba(84,110,241,0.5)'
			break;
		default:
			valBgColor = 'linear-gradient(90deg, #1669e3 0%, #493ee1 100%)';
			valShaColor =
				'0px 2px 8px 0px rgba(22, 105, 227, 0.2), inset 0px -2px 4px 0px rgba(106, 155, 227, 0.4), inset 0px 2px 4px 0px rgba(26, 50, 182, 0.5)'
	}
	return {
		valBgColor,
		valShaColor
	}
}