import { idCardValidate } from './tools'
export default {
  // 用户姓名
  nameRules: [
    { required: true, message: '请输入姓名' },
    { pattern: /^([\u2E80-\uFE4Fa-zA-Z·\\.\s]{1,32})$/, message: '请输入正确的姓名' },
  ],
  // 证件类型
  idTypeRules: [
    { required: true, message: '请选择证件类型', trigger: 'onChange' }
  ],
  // 身份证格式
  idNumRules: [
    { required: true, message: '请输入身份证号' },
    { validator: idCardValidate, message: '请输入正确的身份证号' }
  ],
  // 出生日期
  birthdayRules: [
    { required: true, message: '请选择出生日期', trigger: 'onChange' }
  ],
  // 年龄
  ageRules: [
    { required: true, message: '请输入年龄' },
    { pattern: /^(?:0|[1-9][0-9]?|120)$/, message: '请输入正确的年龄（0-120之间的整数）' }
  ],
  // 性别
  sexRules: [
    { required: true, message: '请选择性别', trigger: 'onChange' }
  ],
  // 手机号
  phoneNoRules: [
    { required: true, message: '请输入联系电话' },
    { pattern: /^[1][0-9][0-9]{9}$/, message: '请输入正确的联系电话' }
  ],
  // 短信验证码
  smsCodeRules: [
    { required: true, message: '请输入验证码' },
    { pattern: /^[0-9]*$/, message: '请输入正确的验证码' }
  ],
  // 体重
  weightRules: [
    { required: true, message: '请输入体重（公斤）' },
    { pattern: /^\d+(\.\d{1})?$/, message: '请输入正确的体重' }
  ],
  // 密码
  passwordRules: [
    { required: true, message: '请输入密码' },
    // { pattern: /^[0-9a-zA-Z]{6,6}$/, message: '请输入正确的密码' }
  ],
  // 银行
  bankNameRules: [
    { required: true, message: '请选择开户银行', trigger: 'onChange' }
  ],
  bankUserNameRules: [
    { required: true, message: '请输入开户人姓名', trigger: 'onChange' }
  ],
  // 支行
  branchNameRules: [
    { required: true, message: '请输入支行名称', trigger: 'onChange' }
  ],
  // 银行卡号
  bankCardNoRules: [
    { required: true, message: '请输入银行卡号' },
  ],
  // 药店
  pharmacyRules: [
    { required: true, message: '请选择购药药店', trigger: 'onChange' }
  ],
  // 关系
  relationRules: [
    { required: true, message: '请选择与患者的关系', trigger: 'onChange' }
  ],
  // 所在地区
  cityRules: [
    { required: true, message: '请选择所在地区', trigger: 'onChange' }
  ],
  // 到店日期
  appointDateRules: [
    { required: true, message: '请选择预约日期', trigger: 'onChange' }
  ],
  // 预约门店
  medicineRules: [
    { required: true, message: '请选择预约药店', trigger: 'onChange' }
  ],
  textRules: [
    { required: true, message: '请输入' },
  ],
  // 结束治疗访视的时间
  buyTimeRules: [
    { required: true, message: '请选择', trigger: 'onChange' }
  ],
  // 最后一次用药日期
  lastTimeRules: [
    { required: true, message: '请选择', trigger: 'onChange' }
  ],
  // 购药医院
  hospitalRules: [
    { required: true, message: '请选择购药医院', trigger: 'onChange' }
  ],
  // 就诊医院
  visitHospitalRules: [
    { required: true, message: '请选择就诊医院', trigger: 'onChange' }
  ],
  // 购药医院
  claimHospital: [
    { required: true, message: '请输入购药医院' }
  ],
  // 处方医院
  chuHospital: [
    { required: true, message: '请输入处方医院' }
  ],
  // 购药药店
  claimPharmacy: [
    { required: true, message: '请输入购药药店' }
  ],
  // 科室
  deptRules: [
    { required: true, message: '请选择科室', trigger: 'onChange' }
  ],
  // 理赔金额
  claimAmountRules: [
    { required: true, message: '请输入补偿金额' },
    { pattern: /^\d+(\.\d+)?$/, message: '请输入正确的补偿金额' },
  ],
  chooseRules: [{ required: true, message: '请选择', trigger: 'onChange' }],

  guardianRules: [
    { required: true, message: '请输入监护人姓名' },
    { pattern: /^([\u2E80-\uFE4Fa-zA-Z·\\.\s]{1,32})$/, message: '请输入正确的监护人姓名' }
  ],
  leadRules: [
    { required: true, message: '请输入代领人姓名' },
    { pattern: /^([\u2E80-\uFE4Fa-zA-Z·\\.\s]{1,32})$/, message: '请输入正确的代领人姓名' }
  ],
  // 医保所在地
  iceCityRules: [
    { required: true, message: '请选择所在地区', trigger: 'onChange' }
  ],
  addressRules: [
    { required: true, message: '请选择收货地址', trigger: 'onChange' }
  ],
  addressDtRules: [
    { required: true, message: '请输入详细地址' },
  ],
  shareCodeRules: [
    { required: true, message: '请输入邀请码' },
    { pattern: /^[0-9a-zA-Z]*$/, message: '请输入正确的邀请码' }
  ],
  countRules: [
    { required: true, message: '请选择所在地区', trigger: 'onChange' }
  ],
  medicalAmountRules: [
    { required: true, message: '请输入' },
    { pattern: /^\d+(\.\d+)?$/, message: '请输入正确金额' },
  ],
}
