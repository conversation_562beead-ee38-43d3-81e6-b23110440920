import {
	defineConfig
} from "vite";
import uni from "@dcloudio/vite-plugin-uni";
// import { resolve } from "path";
import configUrl from './env.js'
// 引入fs模块
import fs from 'fs'

// 读取 manifest.json ，修改后重新写入
const manifestPath = `${__dirname}/manifest.json`;
let Manifest = fs.existsSync(manifestPath) ? fs.readFileSync(manifestPath, {
	encoding: 'utf-8'
}) : '{}'

// 配置替换函数
function replaceManifest(path, value) {
	const arr = path.split('.');
	const len = arr.length;
	const lastItem = arr[len - 1];
	let i = 0;
	let ManifestArr = Manifest.split(/\n/);

	for (let index = 0; index < ManifestArr.length; index++) {
		const item = ManifestArr[index];
		if (new RegExp(`"${arr[i]}"`).test(item)) ++i;
		if (i === len) {
			const hasComma = /,/.test(item);
			ManifestArr[index] = item.replace(
				new RegExp(`"${lastItem}"[\\s\\S]*:[\\s\\S]*`),
				`"${lastItem}": ${typeof value === 'string'? '"'+value+'"' : value}${hasComma ? ',' : ''}`
			);
			break;
		}
	}
	Manifest = ManifestArr.join('\n');
}

// 处理环境变量
try {
	const customDefine = process.env.UNI_CUSTOM_DEFINE || '{"ENV_TYPE":"development"}'
	const envConfig = JSON.parse(customDefine)
	const appid = configUrl[envConfig.ENV_TYPE]?.appid || 'wx_default_appid'

	if (appid) {
		replaceManifest('mp-weixin.appid', appid)
		fs.writeFileSync(manifestPath, Manifest, {
			flag: 'w'
		})
	}
} catch (e) {
	console.error('环境变量处理失败:', e)
}

// export default defineConfig(() => {
// 	return {
// 		plugins: [uni()],
// 		define: {
// 			'process.env.config': configUrl, // 导入环境
// 		},
// 		optimizeDeps: {
// 			include: ['vant'] // 添加 Vant 到预构建依赖
// 		},
// 	}
// })


export default defineConfig(() => {
	return {
		plugins: [uni()],
		define: {
			'process.env.config': configUrl,
		},
		optimizeDeps: {
			include: ['vant']
		},
		alias: {
			'vue': '@dcloudio/uni-mp-vue'
		},
		server: {
			// 配置代理解决跨域问题
			proxy: {
				'/gateway': {
					target: configUrl[process.env.ENV_TYPE || 'development'].baseUrl, // 你的后端服务器地址
					changeOrigin: true,
					rewrite: (path) => path.replace(/^\/gateway/, ''),
					// 如果需要，可以配置更多选项
					secure: false, // 如果是https接口，需要配置这个参数
					// ws: true, // 代理websockets
				},
			}
		},
		// 如果需要，可以配置build选项
		build: {
			// 构建配置
		},
	}
})